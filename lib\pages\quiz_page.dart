import 'dart:async';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:html/parser.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:projet/pages/review_page.dart';
import 'package:projet/models/score.dart';
import 'package:projet/pages/score.service.dart';
import 'package:translator/translator.dart';
import 'package:video_player/video_player.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:audioplayers/audioplayers.dart';


import '../models/audio_manager.dart'; // pour des polices élégantes

class QuizPage extends StatefulWidget {
  final Map<String, dynamic> quizSettings;

  const QuizPage({Key? key, required this.quizSettings}) : super(key: key);

  @override
  _QuizPageState createState() => _QuizPageState();
}

class _QuizPageState extends State<QuizPage> with SingleTickerProviderStateMixin {
  List<dynamic> questions = [];
  int currentQuestionIndex = 0;
  int score = 0;
  int timer = 10;
  int initialTimer = 10;
  Timer? countdownTimer;
  bool isAnswered = false;
  Color? correctAnswerColor;
  Color? wrongAnswerColor;
  List<String> answers = [];
  late AnimationController _animationController;
  late VideoPlayerController _videoController;
  dynamic extraQuestion;
  final AudioManager _audioManager = AudioManager();
  final AudioPlayer audioPlayer = AudioPlayer();
  late bool _isSoundEnabled ;
  final translator = GoogleTranslator();// Variable pour la question supplémentaire

  @override
  void initState() {
    super.initState();
    getQuestionsData();
    _loadSettings();

    // Initialiser l'animation
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 10),
    );

    // Initialiser le contrôleur de vidéo
    _videoController = VideoPlayerController.asset('images/clock.mp3')
      ..initialize().then((_) {
        setState(() {});
        _videoController.setLooping(true);
      });
  }
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isSoundEnabled = prefs.getBool('Sound') ?? true;

    });
  }

  @override
  void dispose() {
    countdownTimer?.cancel();
    _animationController.dispose();
    _videoController.dispose();
    super.dispose();
  }


  // Méthode pour récupérer les questions depuis l'API
  // Méthode pour récupérer les questions depuis l'API
  Future<void> getQuestionsData() async {
    final category = widget.quizSettings['category'];
    final difficulty = widget.quizSettings['difficulty'];
    final amount = widget.quizSettings['amount'];
    final categoryName = widget.quizSettings['categoryName'];

    // Ajuster le chronomètre selon la difficulté
    switch (difficulty) {
      case 'easy':
        initialTimer = 20;
        break;
      case 'medium':
        initialTimer = 15;
        break;
      default:
        initialTimer = 10;
    }

    final url = 'https://opentdb.com/api.php?amount=${amount + 1}&category=$category&difficulty=$difficulty&type=multiple'; // Ajouter 1 question en plus
    final response = await http.get(Uri.parse(url));

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      setState(() {
        questions = data['results'].map((question) {
          question['question'] =
              parse(question['question']).body?.text ?? question['question'];
          question['correct_answer'] =
              parse(question['correct_answer']).body?.text ?? question['correct_answer'];
          question['incorrect_answers'] = question['incorrect_answers']
              .map((answer) => parse(answer).body?.text ?? answer)
              .toList();
          return question;
        }).toList();

        // Définir la question supplémentaire
        extraQuestion = questions.removeLast(); // La dernière question devient la question supplémentaire

        answers = List<String>.from(questions[currentQuestionIndex]['incorrect_answers']);
        answers.add(questions[currentQuestionIndex]['correct_answer']);
        answers.shuffle();
        startTimer();
      });
    } else {
      print('Erreur lors de la récupération des questions');
    }
  }
  // Démarrage du chronomètre
  void startTimer() {
    timer = initialTimer;
    _animationController.repeat();
    _videoController.play();

    countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (this.timer > 0) {
          this.timer--;
        } else {
          nextQuestion();
        }
      });
    });
  }

  // Passer à la question suivante
  void nextQuestion() {
    countdownTimer?.cancel();
    _animationController.reset();
    _videoController.pause();

    if (currentQuestionIndex < questions.length - 1) {
      setState(() {
        currentQuestionIndex++;
        isAnswered = false;
        correctAnswerColor = null;
        wrongAnswerColor = null;
        answers =
        List<String>.from(questions[currentQuestionIndex]['incorrect_answers']);
        answers.add(questions[currentQuestionIndex]['correct_answer']);
        answers.shuffle();

        startTimer();
      });
    } else {
      endQuiz();
    }
  }

  // Terminer le quiz
  void endQuiz() {
    countdownTimer?.cancel();

    // Check for null values before using them
    String categoryName = widget.quizSettings['categoryName'] ?? 'Unknown Category';
    String difficulty = widget.quizSettings['difficulty'] ?? 'Unknown Difficulty';

    saveQuizScore(categoryName, difficulty, score, questions.length);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Quiz Terminé'),
        content: Text('Votre score est de $score / ${questions.length}'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Ferme le dialog
              restartQuiz(); // Redémarre le quiz
            },
            child: const Text('Rejouer'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Ferme le dialog
              Navigator.pushNamed(context, '/'); // Redirige vers la page des paramètres
            },
            child: const Text('Retour à l\'accueil'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Ferme le dialog
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ReviewPage(results: getResults()), // Envoi des résultats à ReviewPage
                ),
              );
            },
            child: const Text('Voir Résultats'),
          ),
        ],
      ),
    );
  }

// Obtenez les résultats sous forme de liste
  List<Map<String, dynamic>> getResults() {
    List<Map<String, dynamic>> results = [];
    for (var question in questions) {
      // Créer une liste des réponses possibles pour chaque question
      List<String> answers = List<String>.from(question['incorrect_answers']);
      answers.add(question['correct_answer']);
      answers.shuffle();

      results.add({
        'question': question['question'],
        'userAnswer': question['userAnswer'] ?? 'Non répondu',
        'correctAnswer': question['correct_answer'],
        'answers': answers,
      });
    }
    return results;
  }

  Future<void> saveQuizScore(String category, String difficulty, int score, int totalQuestions) async {
    final newScore = Score(category: category, difficulty: difficulty, score: score, totalQuestions: totalQuestions);
    ScoreService.saveScore(newScore);
  }


  // Rejouer le quiz
  void restartQuiz() {
    setState(() {
      score = 0;
      currentQuestionIndex = 0;
      isAnswered = false;
      timer = initialTimer;
      getQuestionsData();
    });
  }

  String selectedAnswer = ''; // Ajout d'une variable pour garder la réponse sélectionnée

  void checkAnswer(String answer) {
    setState(() {
      isAnswered = true;
      selectedAnswer = answer;
      String correctAnswer = questions[currentQuestionIndex]['correct_answer'];

      // Sauvegarder la réponse utilisateur dans la question
      questions[currentQuestionIndex]['userAnswer'] = answer;

      if (answer == correctAnswer) {
        if(_isSoundEnabled)
          audioPlayer.play(AssetSource('vrai.mp3'));
        score++;
        correctAnswerColor = Colors.green;
        // Bonne réponse en vert
      } else {
        if(_isSoundEnabled)
          audioPlayer.play(AssetSource('faux.mp3'));
        wrongAnswerColor = Colors.red; // Mauvaise réponse en rouge
      }

      // Attendre une seconde avant de passer à la question suivante
      Future.delayed(const Duration(seconds: 3), () {
        nextQuestion();
      });
    });
  }

  void useFiftyFifty() {

    if (answers.length > 2) {
      setState(() {
        answers = [
          questions[currentQuestionIndex]['correct_answer'],
          ...answers.where((answer) => answer != questions[currentQuestionIndex]['correct_answer']).take(1)
        ];
        answers.shuffle();
        setState(() {
          isFiftyFiftyUsed = true;
        });
      });
    }
  }

// Méthode pour simuler les pourcentages du public
  void askAudience() {
    setState(() {
      // Simule des pourcentages pour chaque réponse
      List<int> percentages = [70, 10, 30, 50];
      percentages.shuffle();

      showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            title: Text("Avis du public"),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: answers.asMap().entries.map((entry) {
                int index = entry.key;
                String answer = entry.value;
                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(answer, style: TextStyle(fontSize: 16)),
                    Text('${percentages[index]}%', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                  ],
                );
              }).toList(),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text("OK"),
              )
            ],
          );
        },
      );
    });
    setState(() {
      isAskAudienceUsed = true;
    });
  }

  void changeQuestion() {
    setState(() {
      // Remplacer la question actuelle par la question supplémentaire
      questions[currentQuestionIndex] = {
        'question': parse(extraQuestion['question']).body?.text ?? extraQuestion['question'],
        'correct_answer': parse(extraQuestion['correct_answer']).body?.text ?? extraQuestion['correct_answer'],
        'incorrect_answers': extraQuestion['incorrect_answers']
            .map((answer) => parse(answer).body?.text ?? answer)
            .toList(),
      };

      // Mettre à jour et mélanger les réponses
      answers = List<String>.from(questions[currentQuestionIndex]['incorrect_answers']);
      answers.add(questions[currentQuestionIndex]['correct_answer']);
      answers.shuffle();

      // Réinitialiser les couleurs et l'état
      isAnswered = false;
      selectedAnswer = '';
      correctAnswerColor = null;
      wrongAnswerColor = null;
    });

    setState(() {
      isChangeQuestionUsed = true;
    });
  }



  bool isFiftyFiftyUsed = false;
  bool isAskAudienceUsed = false;
  bool isChangeQuestionUsed = false;

  @override
  Widget build(BuildContext context) {
    if (questions.isEmpty) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    final currentQuestion = questions[currentQuestionIndex];
    final Size screenSize = MediaQuery
        .of(context)
        .size;

    return Scaffold(
      backgroundColor: Colors.grey[900],
      appBar: AppBar(
        title: const Text('Smart Quiz', style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.blueGrey[800],
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Barre de progression
            LinearProgressIndicator(
              value: (currentQuestionIndex + 1) / questions.length,
              backgroundColor: Colors.blueGrey,
              color: Colors.lightBlueAccent,
            ),
            const SizedBox(height: 20),

            // Titre de la question
            Text(
              'Question ${currentQuestionIndex + 1} / ${questions.length}',
              style: GoogleFonts.roboto(
                fontSize: 18,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),

            // Conteneur de la question
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.blueGrey[700],
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 10,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: Text(
                currentQuestion['question'],
                style: GoogleFonts.roboto(
                  fontSize: 18,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 20),

            // Liste des réponses
            Expanded(
              child: ListView.builder(
                itemCount: answers.length,
                itemBuilder: (context, index) {
                  final answer = answers[index];
                  Color answerColor;

                  // Définir la couleur de l'option sélectionnée
                  if (isAnswered) {
                    if (answer == questions[currentQuestionIndex]['correct_answer']) {
                      answerColor = Colors.green; // Bonne réponse en vert
                    } else if (answer == selectedAnswer) {
                      answerColor = Colors.red; // Réponse incorrecte sélectionnée en rouge
                    } else {
                      answerColor = Colors.blueGrey[300]!;
                    }
                  } else {
                    answerColor = Colors.blueGrey[300]!;
                  }

                  return GestureDetector(
                    onTap: isAnswered ? null : () => checkAnswer(answer),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                      padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 12),
                      margin: const EdgeInsets.symmetric(vertical: 6),
                      decoration: BoxDecoration(
                        color: answerColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black54,
                            blurRadius: 5,
                            offset: Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Text(
                        answer,
                        textAlign: TextAlign.center,
                        style: GoogleFonts.roboto(
                          fontSize: 18,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Opacity(
                  opacity: isFiftyFiftyUsed ? 0.5 : 1.0, // Réduit l'opacité quand utilisé
                  child: IconButton(
                    icon: Icon(Icons.filter_2, color: Colors.blueAccent, size: 30),
                    onPressed: isFiftyFiftyUsed ? null : useFiftyFifty,
                    tooltip: '50:50',
                  ),
                ),
                Opacity(
                  opacity: isAskAudienceUsed ? 0.5 : 1.0, // Réduit l'opacité quand utilisé
                  child: IconButton(
                    icon: Icon(Icons.people, color: Colors.greenAccent, size: 30),
                    onPressed: isAskAudienceUsed ? null : askAudience,
                    tooltip: 'Avis du public',
                  ),
                ),
                Opacity(
                  opacity: isChangeQuestionUsed ? 0.5 : 1.0, // Réduit l'opacité quand utilisé
                  child: IconButton(
                    icon: Icon(Icons.refresh, color: Colors.orangeAccent, size: 30),
                    onPressed: isChangeQuestionUsed ? null : changeQuestion,
                    tooltip: 'Changer la question',
                  ),
                ),
              ],
            ),

            // Affichage du timer avec une animation
            Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  width: screenSize.width * 0.7,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.blueGrey[700],
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                Positioned(
                  left: 0,
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 100),
                    width: (screenSize.width * 0.7) * (timer / initialTimer),
                    height: 12,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colors.redAccent, Colors.yellowAccent],
                      ),
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ),
                Text(
                  '$timer s',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Bouton vidéo
            /*ClipOval(
              child: SizedBox(
                width: 100,
                height: 100,
                child: _videoController.value.isInitialized
                    ? VideoPlayer(_videoController)
                    : const CircularProgressIndicator(),
              ),
            ), */
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}

