import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:projet/pages/quiz.settings_page.dart';

class ReviewPage extends StatefulWidget {
  final List<Map<String, dynamic>> results;

  const ReviewPage({Key? key, required this.results}) : super(key: key);

  @override
  _ReviewPageState createState() => _ReviewPageState();
}

class _ReviewPageState extends State<ReviewPage> {
  int currentIndex = 0;
  PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[900],
      appBar: AppBar(
        title: const Text('Résultats du Quiz', style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.blueGrey[800],
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () {
            Navigator.pop(context); // Ferme la page actuelle
            Navigator.pushNamed(context, '/'); // Redirige vers la page des paramètres
          },
        ),
      ),

      body: Column(
        children: [
          // Barre de progression
          LinearProgressIndicator(
            value: (currentIndex + 1) / widget.results.length,
            backgroundColor: Colors.blueGrey,
            color: Colors.lightBlueAccent,
          ),
          const SizedBox(height: 20),

          // PageView pour la navigation par swipe
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  currentIndex = index;
                });
              },
              itemCount: widget.results.length,
              itemBuilder: (context, index) {
                final result = widget.results[index];
                final List<String> answers = result['answers'] ?? [];
                final String correctAnswer = result['correctAnswer'];
                final String userAnswer = result['userAnswer'];

                return Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Titre de la question
                      Text(
                        'Question ${index + 1} / ${widget.results.length}',
                        style: GoogleFonts.roboto(
                          fontSize: 18,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Conteneur de la question
                      Container(
                        padding: const EdgeInsets.all(16.0),
                        decoration: BoxDecoration(
                          color: Colors.blueGrey[700],
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black26,
                              blurRadius: 10,
                              offset: Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Text(
                          result['question'],
                          style: GoogleFonts.roboto(
                            fontSize: 18,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Affichage des réponses
                      Expanded(
                        child: ListView.builder(
                          itemCount: answers.length,
                          itemBuilder: (context, answerIndex) {
                            final answer = answers[answerIndex];
                            Color answerColor;

                            // Définir la couleur de l'option sélectionnée
                            if (answer == correctAnswer) {
                              answerColor = Colors.green; // Bonne réponse en vert
                            } else if (answer == userAnswer) {
                              answerColor = Colors.red; // Réponse incorrecte sélectionnée en rouge
                            } else {
                              answerColor = Colors.blueGrey[300]!;
                            }

                            return AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                              padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 12),
                              margin: const EdgeInsets.symmetric(vertical: 6),
                              decoration: BoxDecoration(
                                color: answerColor,
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black54,
                                    blurRadius: 5,
                                    offset: Offset(0, 3),
                                  ),
                                ],
                              ),
                              child: Text(
                                answer,
                                textAlign: TextAlign.center,
                                style: GoogleFonts.roboto(
                                  fontSize: 18,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Affichage de l'interprétation
                      Text(
                        userAnswer == correctAnswer
                            ? "Good job! that's the right answer."
                            : "Soory, the right answer is : $correctAnswer.",
                        style: GoogleFonts.roboto(
                          fontSize: 16,
                          color: userAnswer == correctAnswer ? Colors.green : Colors.red,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
