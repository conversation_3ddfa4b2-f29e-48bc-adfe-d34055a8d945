import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:projet/pages/quiz.settings_page.dart';

class ReviewPage extends StatefulWidget {
  final List<Map<String, dynamic>> results;

  const ReviewPage({Key? key, required this.results}) : super(key: key);

  @override
  _ReviewPageState createState() => _ReviewPageState();
}

class _ReviewPageState extends State<ReviewPage> {
  int currentIndex = 0;
  PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Résultats du Quiz',
          style: GoogleFonts.roboto(
            color: Theme.of(context).appBarTheme.foregroundColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: Icon<PERSON><PERSON>on(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, '/');
          },
        ),
      ),

      body: Column(
        children: [
          // Barre de progression
          Container(
            margin: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Question ${currentIndex + 1}',
                      style: GoogleFonts.roboto(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).textTheme.bodyLarge?.color,
                      ),
                    ),
                    Text(
                      '${currentIndex + 1} / ${widget.results.length}',
                      style: GoogleFonts.roboto(
                        fontSize: 14,
                        color: Theme.of(context).textTheme.bodyMedium?.color,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: (currentIndex + 1) / widget.results.length,
                  backgroundColor: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey[700]
                      : Colors.grey[300],
                  color: Colors.deepPurpleAccent,
                  minHeight: 6,
                ),
              ],
            ),
          ),

          // PageView pour la navigation par swipe
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  currentIndex = index;
                });
              },
              itemCount: widget.results.length,
              itemBuilder: (context, index) {
                final result = widget.results[index];
                final List<String> answers = result['answers'] ?? [];
                final String correctAnswer = result['correctAnswer'];
                final String userAnswer = result['userAnswer'];

                return Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Indicateur de résultat
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: userAnswer == correctAnswer
                              ? Colors.green.shade50
                              : Colors.red.shade50,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: userAnswer == correctAnswer
                                ? Colors.green.shade200
                                : Colors.red.shade200,
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              userAnswer == correctAnswer
                                  ? Icons.check_circle
                                  : Icons.cancel,
                              color: userAnswer == correctAnswer
                                  ? Colors.green.shade600
                                  : Colors.red.shade600,
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              userAnswer == correctAnswer
                                  ? 'Bonne réponse !'
                                  : 'Réponse incorrecte',
                              style: GoogleFonts.roboto(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: userAnswer == correctAnswer
                                    ? Colors.green.shade700
                                    : Colors.red.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Conteneur de la question
                      Container(
                        padding: const EdgeInsets.all(20.0),
                        decoration: BoxDecoration(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey[800]
                              : Colors.grey[50],
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.grey[600]!
                                : Colors.grey[300]!,
                            width: 1.5,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.2),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Text(
                          result['question'],
                          style: GoogleFonts.roboto(
                            fontSize: 18,
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Affichage des réponses
                      Expanded(
                        child: ListView.builder(
                          itemCount: answers.length,
                          itemBuilder: (context, answerIndex) {
                            final answer = answers[answerIndex];
                            Color answerColor;
                            Color borderColor;
                            Color textColor;
                            IconData? icon;

                            // Définir la couleur et l'icône selon le type de réponse
                            if (answer == correctAnswer) {
                              answerColor = Colors.green.shade400;
                              borderColor = Colors.green.shade600;
                              textColor = Colors.white;
                              icon = Icons.check_circle;
                            } else if (answer == userAnswer) {
                              answerColor = Colors.red.shade400;
                              borderColor = Colors.red.shade600;
                              textColor = Colors.white;
                              icon = Icons.cancel;
                            } else {
                              answerColor = Theme.of(context).brightness == Brightness.dark
                                  ? Colors.grey[700]!
                                  : Colors.grey[100]!;
                              borderColor = Theme.of(context).brightness == Brightness.dark
                                  ? Colors.grey[600]!
                                  : Colors.grey[300]!;
                              textColor = Theme.of(context).textTheme.bodyLarge?.color ?? Colors.black;
                              icon = null;
                            }

                            return Container(
                              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                              margin: const EdgeInsets.symmetric(vertical: 6),
                              decoration: BoxDecoration(
                                color: answerColor,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: borderColor,
                                  width: 1.5,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withOpacity(0.2),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Row(
                                children: [
                                  if (icon != null) ...[
                                    Icon(
                                      icon,
                                      color: textColor,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 12),
                                  ],
                                  Expanded(
                                    child: Text(
                                      answer,
                                      style: GoogleFonts.roboto(
                                        fontSize: 16,
                                        color: textColor,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  if (answer == correctAnswer)
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Text(
                                        'Correcte',
                                        style: GoogleFonts.roboto(
                                          fontSize: 12,
                                          color: Colors.white,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  if (answer == userAnswer && answer != correctAnswer)
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Text(
                                        'Votre choix',
                                        style: GoogleFonts.roboto(
                                          fontSize: 12,
                                          color: Colors.white,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Boutons de navigation
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey[800]
                              : Colors.grey[50],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.grey[600]!
                                : Colors.grey[300]!,
                            width: 1
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: TextButton(
                                onPressed: currentIndex > 0
                                    ? () {
                                  _pageController.previousPage(
                                    duration: const Duration(milliseconds: 300),
                                    curve: Curves.easeInOut,
                                  );
                                }
                                    : null,
                                style: TextButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    side: BorderSide(
                                      color: currentIndex > 0
                                          ? Colors.deepPurpleAccent
                                          : (Theme.of(context).brightness == Brightness.dark
                                              ? Colors.grey[600]!
                                              : Colors.grey[300]!),
                                      width: 1,
                                    ),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.arrow_back_ios,
                                      size: 16,
                                      color: currentIndex > 0
                                          ? Colors.deepPurpleAccent
                                          : (Theme.of(context).brightness == Brightness.dark
                                              ? Colors.grey[500]
                                              : Colors.grey[400]),
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      'Précédent',
                                      style: GoogleFonts.roboto(
                                        color: currentIndex > 0
                                            ? Colors.deepPurpleAccent
                                            : (Theme.of(context).brightness == Brightness.dark
                                                ? Colors.grey[500]
                                                : Colors.grey[400]),
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: TextButton(
                                onPressed: currentIndex < widget.results.length - 1
                                    ? () {
                                  _pageController.nextPage(
                                    duration: const Duration(milliseconds: 300),
                                    curve: Curves.easeInOut,
                                  );
                                }
                                    : () {
                                  Navigator.pop(context);
                                  Navigator.pushNamed(context, '/');
                                },
                                style: TextButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                  backgroundColor: Colors.deepPurpleAccent,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      currentIndex < widget.results.length - 1
                                          ? 'Suivant'
                                          : 'Terminer',
                                      style: GoogleFonts.roboto(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    Icon(
                                      currentIndex < widget.results.length - 1
                                          ? Icons.arrow_forward_ios
                                          : Icons.home,
                                      size: 16,
                                      color: Colors.white,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
