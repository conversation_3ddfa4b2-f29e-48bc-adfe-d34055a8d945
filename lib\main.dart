import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:provider/provider.dart';
import 'package:projet/pages/about_page.dart';
import 'package:projet/services/language_provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'pages/home_page.dart';
import 'pages/quiz_page.dart';
import 'pages/quiz.settings_page.dart';
import 'package:adaptive_theme/adaptive_theme.dart';
import 'pages/score_page.dart';
import 'pages/settings_page.dart';
final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
FlutterLocalNotificationsPlugin();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await initializeNotifications();

  runApp(
    ChangeNotifierProvider(
      create: (_) => LanguageProvider(),
      child: const SmartQuiz(),
    ),
  );
}

// Initialisation des notifications
Future<void> initializeNotifications() async {
  const AndroidInitializationSettings initializationSettingsAndroid =
  AndroidInitializationSettings('@mipmap/ic_launcher');

  const InitializationSettings initializationSettings =
  InitializationSettings(android: initializationSettingsAndroid);

  await flutterLocalNotificationsPlugin.initialize(initializationSettings);
}

// Envoi d'une notification locale
Future<void> sendNotification(String title, String body) async {
  const AndroidNotificationDetails androidPlatformChannelSpecifics =
  AndroidNotificationDetails(
    'channel_id',
    'SmartQuiz Notifications',
    channelDescription: 'Notifications pour SmartQuiz',
    importance: Importance.max,
    priority: Priority.high,
  );

  const NotificationDetails platformChannelSpecifics =
  NotificationDetails(android: androidPlatformChannelSpecifics);

  await flutterLocalNotificationsPlugin.show(
    0, // ID unique pour la notification
    title,
    body,
    platformChannelSpecifics,
  );
}

class SmartQuiz extends StatefulWidget {
  const SmartQuiz({super.key});

  @override
  State<SmartQuiz> createState() => _SmartQuizState();
}

class _SmartQuizState extends State<SmartQuiz> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this); // Attache l'observateur
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this); // Détache l'observateur
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused) {
      // L'application passe en arrière-plan
      sendNotification(
        'Vous partez déjà ?',
        'Revenez sur SmartQuiz pour continuer vos quiz !',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);

    return AdaptiveTheme(
        light: ThemeData(
          brightness: Brightness.light,
          primarySwatch: Colors.blue,
        ),
        dark: ThemeData(
          brightness: Brightness.dark,
          primarySwatch: Colors.blue,
        ),
        initial: AdaptiveThemeMode.light,
        builder: (theme, darkTheme) => MaterialApp(
            title: 'SmartQuiz',
            debugShowCheckedModeBanner: false,
            theme: theme,
            darkTheme: darkTheme,
            locale: languageProvider.locale,
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            initialRoute: '/',
            routes: {
              '/': (context) => HomePage(),
              '/settings': (context) => QuizSettingsPage(),
              '/quiz': (context) => QuizPage(quizSettings: ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>,),
              '/classement': (context) => ScorePage(),
              '/setting': (context) => SettingsPage(),
              '/about': (context) => AboutPage(),
            },
            ),
        );
    }
}