import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:adaptive_theme/adaptive_theme.dart';
import '../models/audio_manager.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';


class HomePage extends StatefulWidget {
  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final AudioManager _audioManager = AudioManager();
  bool _isDarkMode = false;
  bool _isMusicPlaying = false;

  void _toggleMusic() {
    setState(() {
      if (_isMusicPlaying) {
        _audioManager.stopMusic();
      } else {
        _audioManager.playMusic('music.mp3');
      }
      _isMusicPlaying = !_isMusicPlaying;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Smart Quiz'),
        actions: [
          IconButton(
            icon: Icon(
              _isMusicPlaying ? Icons.music_note : Icons.music_off,
            ),
            onPressed: _toggleMusic,
            tooltip: _isMusicPlaying ? 'Désactiver la musique' : 'Activer la musique',
          ),
        ],
      ),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            DrawerHeader(
              decoration: const BoxDecoration(

              ),
              child: Center(
                child: Container(
                  decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black26,
                        blurRadius: 10,
                        offset: Offset(0, 4),
                      ),
                    ],
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: Image.asset(
                      'images/quiz.jpg',
                      width: 150,
                      height: 350,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
            ),

            ListTile(
              leading: const Icon(Icons.home),
              title:  Text(AppLocalizations.of(context)!.home),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/');
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings),
              title:  Text(AppLocalizations.of(context)!.settings),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/setting');
              },
            ),
            ListTile(
              leading: const Icon(Icons.leaderboard),
              title:  Text(AppLocalizations.of(context)!.ranking),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/classement');
              },
            ),
            ListTile(
                leading: const Icon(Icons.info),
                title:  Text(AppLocalizations.of(context)!.about),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.pushNamed(context, '/about');
                },
                ),
            const Divider(),
            ListTile(
              leading: Icon(
                _isDarkMode ? Icons.dark_mode : Icons.light_mode,
              ),
              title: Text(_isDarkMode ? AppLocalizations.of(context)!.dark_mode : AppLocalizations.of(context)!.light_mode),
              onTap: () {
                setState(() {
                  _isDarkMode = !_isDarkMode;
                  if (_isDarkMode) {
                    AdaptiveTheme.of(context).setDark();
                  } else {
                    AdaptiveTheme.of(context).setLight();
                  }
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
      body: Stack(
        children: [
          Center(
            child: Image.asset(
              'images/quiz.jpg',
              width: 450,
              height: 450,
              fit: BoxFit.cover,
            ),
          ),
          Positioned(
            bottom: 30,
            left: 0,
            right: 0,
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ElevatedButton(
                    onPressed: () => Navigator.pushNamed(context, '/settings'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 60, vertical: 15),
                      backgroundColor: const Color(0xFFC6E7FF),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                      elevation: 5,
                    ),
                    child:  Text(
                      AppLocalizations.of(context)!.start,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ),

                ],
              ),
            ),
          ),
        ],
      ),
    );
  }



  @override
  void dispose() {
    _audioManager.stopMusic();
    super.dispose();
  }
}