import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:translator/translator.dart';

class QuizSettingsPage extends StatefulWidget {
  const QuizSettingsPage({Key? key}) : super(key: key);

  @override
  _QuizSettingsPageState createState() => _QuizSettingsPageState();
}

class _QuizSettingsPageState extends State<QuizSettingsPage> {
  List<dynamic> categories = [];
  String? selectedCategory;
  String? selectedDifficulty = 'easy';
  int? selectedNumberOfQuestions;
  double difficultyLevel = 0;
  final translator = GoogleTranslator();


  bool isPageLoading = true; // Indicates if the page is loading
  double progress = 0.0; // Progress for loading

  Map<int, IconData> categoryIcons = {
    9: Icons.language,
    10: Icons.book,
    11: Icons.movie,
    12: Icons.music_note,
    13: Icons.theater_comedy,
    14: Icons.tv,
    15: Icons.videogame_asset,
    16: Icons.extension,
    17: Icons.science,
    18: Icons.computer,
    19: Icons.calculate,
    20: Icons.menu_book,
    21: Icons.sports_soccer,
    22: Icons.map,
    23: Icons.history,
    24: Icons.gavel,
    25: Icons.brush,
    26: Icons.star,
    27: Icons.pets,
    28: Icons.directions_car,
    29: Icons.auto_stories,
    30: Icons.devices_other,
    31: Icons.animation,
    32: Icons.child_care,
  };

  static const Color blueLogo = Color(0xFF00FFF7);
  static const Color pinkLogo = Color(0xFFFF00F3);
  static const Color mediumLogo = Color(0xFF7F7FF5);

  @override
  void initState() {
    super.initState();
    loadPageData();
  }


  Future<void> loadPageData() async {
    // Simulate loading with progress
    for (int i = 0; i <= 100; i++) {
      await Future.delayed(const Duration(milliseconds: 20));
      setState(() {
        progress = i / 100;
      });
    }

    // Fetch categories after progress
    await getCategoriesData();
    setState(() {
      isPageLoading = false;
    });
  }
  Future<void> _loadLanguage() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String savedLanguage = prefs.getString('selectedLanguage') ?? 'en';

  }


  Future<void> getCategoriesData() async {
    try {
      final response = await http.get(Uri.parse('https://opentdb.com/api_category.php'));
      if (response.statusCode == 200) {
        // Fetch the current language from SharedPreferences
        SharedPreferences prefs = await SharedPreferences.getInstance();
        String selectedLanguage = prefs.getString('selectedLanguage') ?? 'en';

        // Parse the categories
        List<dynamic> categories = json.decode(response.body)['trivia_categories'];

        // Translate category names
        List<dynamic> translatedCategories = await Future.wait(
          categories.map((category) async {
            String translatedName = category['name'];
            if (selectedLanguage != 'en') {
              translatedName = (await translator.translate(
                category['name'],
                to: selectedLanguage,
              )).text;
            }
            return {
              'id': category['id'],
              'name': translatedName,
            };
          }),
        );

        setState(() {
          this.categories = translatedCategories; // Ensure you're updating the state correctly
        });
      } else {
        throw Exception("Failed to load categories");
      }
    } catch (error) {
      print("Error fetching or translating categories: $error");
    }
  }

  void _onStartQuiz() {
    if (selectedCategory != null && selectedDifficulty != null && selectedNumberOfQuestions != null) {
      final selectedCategoryName =
      categories.firstWhere((category) => category['id'].toString() == selectedCategory)['name'];
      Navigator.pushNamed(
        context,
        '/quiz',
        arguments: {
          'category': selectedCategory,
          'categoryName': selectedCategoryName,
          'difficulty': selectedDifficulty,
          'amount': selectedNumberOfQuestions,
        },
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(AppLocalizations.of(context)!.fill_all_fields)),
      );
    }
  }

  static Color blueTransparent = blueLogo.withOpacity(0.5);
  static Color pinkTransparent = pinkLogo.withOpacity(0.5);
  static Color mediumTransparent = mediumLogo.withOpacity(0.5);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A19),
      appBar: AppBar(
        elevation: 10,
        title: Text(
          AppLocalizations.of(context)!.quiz_settings,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 22),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              print('Settings icon clicked');
            },
          ),
        ],
      ),
      body: isPageLoading
          ? const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.deepPurpleAccent),
              strokeWidth: 4.0,
            ),
            SizedBox(height: 20),
            Text(
              'Chargement...',
              style: TextStyle(
                fontSize: 18,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      )
          : Container(
        color: Colors.white,
        child:
          SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context)!.choose_category,
                  style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Color(0xFF424242)),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 300,
                  child: ListView.builder(
                    itemCount: categories.length,
                    itemBuilder: (context, index) {
                      final category = categories[index];
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedCategory = category['id'].toString();
                          });
                        },
                        child: Container(
                          margin: const EdgeInsets.symmetric(vertical: 8.0),
                          padding: const EdgeInsets.all(16.0),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            color: selectedCategory == category['id'].toString()
                                ? Colors.deepPurpleAccent
                                : Colors.grey[200],
                            border: Border.all(
                              color: selectedCategory == category['id'].toString()
                                  ? Colors.deepPurpleAccent
                                  : Colors.grey[300]!,
                              width: 1,
                            ),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Icon(
                                categoryIcons[category['id']] ?? Icons.category,
                                color: selectedCategory == category['id'].toString()
                                    ? Colors.white
                                    : const Color(0xFF424242),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  category['name'],
                                  style: TextStyle(
                                    fontSize: 18,
                                    color: selectedCategory == category['id'].toString()
                                        ? Colors.white
                                        : const Color(0xFF424242),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  AppLocalizations.of(context)!.choose_difficulty,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF424242),
                  ),
                ),
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 16.0),
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: LinearGradient(
                      colors: [blueTransparent, pinkTransparent, mediumTransparent],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                  ),
                  child: SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      trackHeight: 12.0,
                      thumbShape: const RoundSliderThumbShape(
                        enabledThumbRadius: 15.0,
                      ),
                      overlayShape: const RoundSliderOverlayShape(
                        overlayRadius: 24.0,
                      ),
                      activeTrackColor: Colors.transparent,
                      inactiveTrackColor: Colors.transparent,
                      thumbColor: Colors.black,
                      overlayColor: Colors.black.withOpacity(0.2),
                    ),
                    child: Slider(
                      value: difficultyLevel,
                      min: 0,
                      max: 1,
                      divisions: 2,
                      onChanged: (value) {
                        setState(() {
                          // Assurez-vous que la valeur reste entre min et max
                          difficultyLevel = value.clamp(0.0, 1.0);
                          if ((difficultyLevel - 0).abs() < 0.01) {
                            selectedDifficulty = 'easy';
                          } else if ((difficultyLevel - 0.5).abs() < 0.01) {
                            selectedDifficulty = 'medium';
                          } else if ((difficultyLevel - 1).abs() < 0.01) {
                            selectedDifficulty = 'hard';
                          }
                        });
                      },
                      label: (difficultyLevel - 0).abs() < 0.01
                          ? AppLocalizations.of(context)!.easy
                          : (difficultyLevel - 0.5).abs() < 0.01
                          ? AppLocalizations.of(context)!.medium
                          : AppLocalizations.of(context)!.hard,
                    ),
                  ),
                ),
                Text(
                  AppLocalizations.of(context)!.choose_number_question,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF424242),
                  ),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<int>(
                  value: selectedNumberOfQuestions,
                  items: const [
                    DropdownMenuItem(
                      value: 5,
                      child: Row(
                        children: [
                          Icon(Icons.filter_5, color: Colors.blue, size: 24),
                          SizedBox(width: 12),
                          Text('5 questions', style: TextStyle(color: Color(0xFF424242), fontSize: 16, fontWeight: FontWeight.w500)),
                        ],
                      ),
                    ),
                    DropdownMenuItem(
                      value: 10,
                      child: Row(
                        children: [
                          Icon(Icons.filter_1, color: Colors.purple, size: 24),
                          Icon(Icons.filter_none, color: Colors.purple, size: 16),
                          SizedBox(width: 8),
                          Text('10 questions', style: TextStyle(color: Color(0xFF424242), fontSize: 16, fontWeight: FontWeight.w500)),
                        ],
                      ),
                    ),
                    DropdownMenuItem(
                      value: 15,
                      child: Row(
                        children: [
                          Icon(Icons.filter_1, color: Colors.cyan, size: 24),
                          Icon(Icons.filter_5, color: Colors.cyan, size: 16),
                          SizedBox(width: 8),
                          Text('15 questions', style: TextStyle(color: Color(0xFF424242), fontSize: 16, fontWeight: FontWeight.w500)),
                        ],
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedNumberOfQuestions = value;
                    });
                  },
                  hint:  Text(
                    AppLocalizations.of(context)!.select_number_of_questions,
                    style: const TextStyle(fontSize: 14, color: Color(0xFF757575)),
                  ),
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: Colors.grey[50],
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFFE0E0E0), width: 1.5),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFFE0E0E0), width: 1.5),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.deepPurpleAccent, width: 2),
                    ),
                  ),
                  dropdownColor: Colors.white,
                  icon: const Icon(Icons.arrow_drop_down, color: Color(0xFF424242), size: 28),
                ),
                const SizedBox(height: 24),
                Center(
                  child: ElevatedButton(
                    onPressed: _onStartQuiz,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 60, vertical: 15),
                      backgroundColor: Color(0xFFC6E7FF),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                      elevation: 5,
                    ),
                    child:  Text(
                      AppLocalizations.of(context)!.start_quiz,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF424242),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
      ),
    );
  }
}
