import 'package:flutter/material.dart';
import 'score.service.dart';
import '../models/score.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';


class ScorePage extends StatefulWidget {
  @override
  _ScorePageState createState() => _ScorePageState();
}

class _ScorePageState extends State<ScorePage> {
  Map<String, Map<String, int>> categoryScores = {};
  final Map<String, IconData> categoryIcons = {
    "General Knowledge": Icons.language,
    "Entertainment: Books": Icons.book,
    "Entertainment: Film": Icons.movie,
    "Entertainment: Music": Icons.music_note,
    "Entertainment: Musicals & Theatres": Icons.theater_comedy,
    "Entertainment: Television": Icons.tv,
    "Entertainment: Video Games": Icons.videogame_asset,
    "Entertainment: Board Games": Icons.extension,
    "Science & Nature": Icons.science,
    "Science: Computers": Icons.computer,
    "Science: Mathematics": Icons.calculate,
    "Mythology": Icons.menu_book,
    "Sports": Icons.sports_soccer,
    "Geography": Icons.map,
    "History": Icons.history,
    "Politics": Icons.gavel,
    "Art": Icons.brush,
    "Celebrities": Icons.star,
    "Animals": Icons.pets,
    "Vehicles": Icons.directions_car,
    "Entertainment: Comics": Icons.auto_stories,
    "Science: Gadgets": Icons.devices_other,
    "Entertainment: Japanese Anime & Manga": Icons.animation,
    "Entertainment: Cartoon & Animations": Icons.child_care,
  };

  @override
  void initState() {
    super.initState();
    fetchScores();
  }

  void fetchScores() async {
    List<Score> fetchedScores = await ScoreService.getAllScores();

    Map<String, Map<String, int>> groupedScores = {};
    for (var score in fetchedScores) {
      groupedScores.putIfAbsent(score.category, () => {'E': 0, 'M': 0, 'H': 0});
      String difficultyKey = score.difficulty[0].toUpperCase();
      groupedScores[score.category]![difficultyKey] =
          (groupedScores[score.category]![difficultyKey] ?? 0) + score.score;
    }

    setState(() {
      categoryScores = groupedScores;
    });
  }

  // Méthode pour supprimer une catégorie
  void deleteCategory(String category) async {
    final confirmed = await _showDeleteConfirmationDialog(context, category);
    if (confirmed == true) {
      setState(() {
        // Supprimer la catégorie de la carte
        categoryScores.remove(category);
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Category "$category" has been deleted')),
      );
    }
  }

  // Confirmation de suppression
  Future<bool?> _showDeleteConfirmationDialog(
      BuildContext context, String category) {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Delete Category: $category'),
          content: const Text('Are you sure you want to delete this category?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(false); // Return false if canceled
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(true); // Return true if confirmed
              },
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }

  IconData? getCategoryIcon(String categoryName) {
    return categoryIcons[categoryName] ?? Icons.help_outline;
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title:  Text(AppLocalizations.of(context)!.leaderboard),
      ),
      body: categoryScores.isEmpty
          ?  Center(child: Text(AppLocalizations.of(context)!.noScoresRecorded))
          : Stack(
        children: [
          // Image d'arrière-plan
          Opacity(
            opacity: 0.3, // 50% de transparence
            child: Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage("images/rank1.webp"),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),

          // Contenu principal au premier plan
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(
              columnSpacing: 20,
              columns: [
                DataColumn(
                  label: Text(
                    AppLocalizations.of(context)!.category,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                 DataColumn(
                  label: Text(
                    AppLocalizations.of(context)!.e,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                 DataColumn(
                  label: Text(
                    AppLocalizations.of(context)!.m,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                 DataColumn(
                  label: Text(
                    AppLocalizations.of(context)!.h,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                 DataColumn(
                  label: Text(
                    AppLocalizations.of(context)!.total,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
              rows: categoryScores.entries.map((entry) {
                final category = entry.key;
                final scores = entry.value;
                final total = (scores['E'] ?? 0) +
                    (scores['M'] ?? 0) +
                    (scores['H'] ?? 0);

                return DataRow(
                  onLongPress: () => deleteCategory(category), // Appui long pour suppression
                  cells: [
                    DataCell(
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Icon(
                            getCategoryIcon(category),
                            size: 18,
                          ),
                          const SizedBox(width: 8),
                          SizedBox(
                            width: 165, // Largeur fixe
                            child: Text(
                              category,
                              style: const TextStyle(fontSize: 14),
                              softWrap: true,
                              maxLines: 5,
                              overflow: TextOverflow.visible,
                            ),
                          )
                        ],
                      ),
                    ),
                    DataCell(Text('${scores['E']}')), // Score Easy
                    DataCell(Text('${scores['M']}')), // Score Medium
                    DataCell(Text('${scores['H']}')), // Score Hard
                    DataCell(Text('$total')), // Total Score
                  ],
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

}
