class Score {
  final String category;
  final String difficulty;
  final int score; // Nombre de bonnes réponses
  final int totalQuestions; // Total des questions du quiz

  Score({
    required this.category,
    required this.difficulty,
    required this.score,
    required this.totalQuestions,
  });

  Map<String, dynamic> toMap() {
    return {
      'category': category,
      'difficulty': difficulty,
      'score': score,
      'totalQuestions': totalQuestions,
    };
  }

  factory Score.fromMap(Map<String, dynamic> map) {
    return Score(
      category: map['category'],
      difficulty: map['difficulty'],
      score: map['score'],
      totalQuestions: map['totalQuestions'],
    );
  }
}
