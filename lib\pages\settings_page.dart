import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_fonts/google_fonts.dart';

import '../services/language_provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
FlutterLocalNotificationsPlugin();

class SettingsPage extends StatefulWidget {


  @override
  _SettingsPageState createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  bool _isSoundEnabled = true;
  final AudioPlayer _touchSoundPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isSoundEnabled = prefs.getBool('Sound') ?? true;
    });
  }

  String _getAudioSettingsTitle(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    switch (languageProvider.locale.languageCode) {
      case 'ar':
        return 'إعدادات الصوت';
      case 'fr':
        return 'Paramètres Audio';
      case 'en':
      default:
        return 'Audio Settings';
    }
  }

  String _getEnableSoundTitle(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    switch (languageProvider.locale.languageCode) {
      case 'ar':
        return 'تفعيل الصوت';
      case 'fr':
        return 'Activer le son';
      case 'en':
      default:
        return 'Enable Sound';
    }
  }

  String _getSoundDescription(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    switch (languageProvider.locale.languageCode) {
      case 'ar':
        return 'أصوات الإجابات الصحيحة/الخاطئة';
      case 'fr':
        return 'Sons des réponses correctes/incorrectes';
      case 'en':
      default:
        return 'Correct/incorrect answer sounds';
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context)!.settings,
          style: GoogleFonts.roboto(
            fontWeight: FontWeight.bold,
            fontSize: 22,
            color: Theme.of(context).appBarTheme.foregroundColor,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),

            // Language Section
            _buildSectionTitle(
              context,
              AppLocalizations.of(context)!.select_language,
              Icons.language,
            ),

            const SizedBox(height: 16),

            _buildLanguageCard(context, languageProvider),

            const SizedBox(height: 40),

            // Sound Section
            _buildSectionTitle(
              context,
              _getAudioSettingsTitle(context),
              Icons.volume_up,
            ),

            const SizedBox(height: 16),

            _buildSoundCard(context),

            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.deepPurpleAccent.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: Colors.deepPurpleAccent,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: GoogleFonts.roboto(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).textTheme.titleLarge?.color,
          ),
        ),
      ],
    );
  }

  Widget _buildLanguageCard(BuildContext context, LanguageProvider languageProvider) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardTheme.color,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDark ? Colors.grey[700]! : Colors.grey[200]!,
          width: 1
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withOpacity(0.3)
                : Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context)!.language,
              style: GoogleFonts.roboto(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).textTheme.titleMedium?.color,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              decoration: BoxDecoration(
                border: Border.all(
                  color: isDark ? Colors.grey[600]! : Colors.grey[300]!,
                  width: 1
                ),
                borderRadius: BorderRadius.circular(12),
                color: isDark ? Colors.grey[800] : Colors.grey[50],
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: languageProvider.locale.languageCode,
                  isExpanded: true,
                  style: GoogleFonts.roboto(
                    fontSize: 16,
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: 'en',
                      child: Row(
                        children: [
                          Text('🇺🇸'),
                          SizedBox(width: 8),
                          Text('English'),
                        ],
                      ),
                    ),
                    DropdownMenuItem(
                      value: 'fr',
                      child: Row(
                        children: [
                          Text('🇫🇷'),
                          SizedBox(width: 8),
                          Text('Français'),
                        ],
                      ),
                    ),
                    DropdownMenuItem(
                      value: 'ar',
                      child: Row(
                        children: [
                          Text('🇸🇦'),
                          SizedBox(width: 8),
                          Text('العربية'),
                        ],
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      languageProvider.changeLanguage(value);
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSoundCard(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardTheme.color,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDark ? Colors.grey[700]! : Colors.grey[200]!,
          width: 1
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withOpacity(0.3)
                : Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _isSoundEnabled
                    ? Colors.green.withOpacity(0.1)
                    : Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                _isSoundEnabled ? Icons.volume_up : Icons.volume_off,
                color: _isSoundEnabled ? Colors.green : Colors.grey,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getEnableSoundTitle(context),
                    style: GoogleFonts.roboto(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).textTheme.titleMedium?.color,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _getSoundDescription(context),
                    style: GoogleFonts.roboto(
                      fontSize: 14,
                      color: isDark ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Switch(
              value: _isSoundEnabled,
              onChanged: (value) {
                setState(() {
                  _isSoundEnabled = value;
                  _saveSettings();
                });
              },
              activeColor: Colors.deepPurpleAccent,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setBool('Sound', _isSoundEnabled);
  }

  @override
  void dispose() {
    _touchSoundPlayer.dispose();
    super.dispose();
  }
}