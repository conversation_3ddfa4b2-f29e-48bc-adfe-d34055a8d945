import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../services/language_provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import 'home_page.dart';

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
FlutterLocalNotificationsPlugin();

class SettingsPage extends StatefulWidget {


  @override
  _SettingsPageState createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  bool _isSoundEnabled = true;
  bool _isTouchSoundEnabled = true;
  final Map<String, String> _languageOptions = {
    'en': 'English',
    'fr': 'Français',
    'ar': 'العربية',
  };

  final AudioPlayer _touchSoundPlayer = AudioPlayer();

  // Variable for language selection
  String _selectedLanguage = 'en'; // Default language

  Future<void> _playTouchSound() async {
    if (_isTouchSoundEnabled) {
      await _touchSoundPlayer.play(AssetSource('sound.mp3'));
    }
  }
  @override
  void initState() {
    super.initState();
    _loadSettings();
  }
  Future<void> _saveLanguagePreference(String languageCode) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString('selectedLanguage', languageCode);
  }

  void _changeLanguage(String languageCode) async {
    setState(() {
      _selectedLanguage = languageCode;
    });
    await _saveLanguagePreference(languageCode);

    // Optionally show a toast/snackbar to notify the user
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Language set to ${_languageOptions[languageCode]}'),
        duration: const Duration(seconds: 2),
      ),
    );


  }

  Future<void> _showNotification() async {
    const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'channel_id',
      'channel_name',
      channelDescription: 'Description du canal',
      importance: Importance.high,
      priority: Priority.high,
    );

    const NotificationDetails platformDetails = NotificationDetails(android: androidDetails);

    await flutterLocalNotificationsPlugin.show(
      0,
      'Notification de Test',
      'Ceci est une notification test.',
      platformDetails,
    );
  }
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isSoundEnabled = prefs.getBool('Sound') ?? true;

    });
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setBool('Sound', _isSoundEnabled);

  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.settings),
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [



            const SizedBox(height: 40),

            // Language Selection Section
            Text(
              AppLocalizations.of(context)!.select_language,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            ListTile(
              title: Text(AppLocalizations.of(context)!.language),
              trailing: DropdownButton<String>(
                value: languageProvider.locale.languageCode, // Current language
                items: [
                  DropdownMenuItem(value: 'en', child: Text('English')),
                  DropdownMenuItem(value: 'fr', child: Text('Français')),
                  DropdownMenuItem(value: 'ar', child: Text('العربية')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    languageProvider.changeLanguage(value); // Update language
                  }
                },
              ),
            ),
            ListTile(
              leading: Icon(Icons.language),
              title: Text('Language'),
              trailing: DropdownButton<String>(
                value: _selectedLanguage,
                onChanged: (String? newLang) {
                  if (newLang != null) {
                    _changeLanguage(newLang);
                  }
                },
                items: _languageOptions.entries.map((entry) {
                  return DropdownMenuItem(
                    value: entry.key,
                    child: Text(entry.value),
                  );
                }).toList(),
              ),
            ),
            const SizedBox(height: 40),

            // Switch pour activer/désactiver le son "vrai"
            SwitchListTile(
              title: Text('Activer le son'),
              value: _isSoundEnabled,
              onChanged: (value) {
                setState(() {
                  _isSoundEnabled = value;
                  _saveSettings();
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _touchSoundPlayer.dispose();
    super.dispose();
    }
}