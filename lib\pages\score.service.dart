import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/score.dart';

class ScoreService {
  static const String scoresKey = 'best_scores';

  // Méthode pour sauvegarder un score
  static Future<void> saveScore(Score score) async {
    final prefs = await SharedPreferences.getInstance();
    List<String> scoresList = prefs.getStringList(scoresKey) ?? [];

    // Ajouter le nouveau score à la liste
    scoresList.add(jsonEncode(score.toMap()));

    // Sauvegarder la liste mise à jour
    await prefs.setStringList(scoresKey, scoresList);
  }

  // Sauvegarder un score depuis saveQuizScore
  Future<void> saveQuizScore(String category, String difficulty, int score, int totalQuestions) async {
    final newScore = Score(
      category: category,
      difficulty: difficulty,
      score: score,
      totalQuestions: totalQuestions,
    );
    await saveScore(newScore); // Sauvegarder le score
  }

  // Récupérer tous les scores
  static Future<List<Score>> getAllScores() async {
    final prefs = await SharedPreferences.getInstance();
    List<String> scoresList = prefs.getStringList(scoresKey) ?? [];

    return scoresList.map((scoreStr) {
      Map<String, dynamic> scoreMap = jsonDecode(scoreStr);
      return Score.fromMap(scoreMap);
    }).toList();
  }

  // Réinitialiser les scores
  static Future<void> resetScores() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(scoresKey);
  }
}
