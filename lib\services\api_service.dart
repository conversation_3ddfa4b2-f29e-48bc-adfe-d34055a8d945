import 'dart:convert';
import 'package:http/http.dart' as http;

class ApiService {
  static const String baseUrl = 'https://opentdb.com/api.php';

  static Future<List<dynamic>> fetchQuestions(int amount, int category, String difficulty) async {
    final url = Uri.parse('$baseUrl?amount=$amount&category=$category&difficulty=$difficulty&type=multiple');
    final response = await http.get(url);

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return data['results'];
    } else {
      throw Exception('Failed to load questions');
    }
  }
}
