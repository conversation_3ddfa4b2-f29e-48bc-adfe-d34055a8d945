import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutPage extends StatelessWidget {
  const AboutPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final bool isRTL = Directionality.of(context) == TextDirection.rtl; // Définir isRTL ici

    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.about_title),
        backgroundColor: isDarkMode ? const Color(0xFF1A1A1A) : const Color(0xFFC6E7FF),
        elevation: 0,
      ),
      extendBodyBehindAppBar: true,
      body: Directionality(
        textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr, // Appliquer la direction ici
        child: Stack(
          children: [
            Opacity(
              opacity: 0.3,
              child: Container(
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage("images/about_img.jpg"),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(height: 100),
                  Text(
                    AppLocalizations.of(context)!.about_description,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                  const SizedBox(height: 220),
                  Text(
                    AppLocalizations.of(context)!.meet_the_developers,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                  const SizedBox(height: 20),
                  _buildDeveloperCard(
                    context: context,
                    name: AppLocalizations.of(context)!.daly,
                    role: AppLocalizations.of(context)!.mobile_dev,
                    imageUrl: 'images/fatma.jpg',
                    linkedInUrl: 'https://www.linkedin.com/in/mohamed-ali-zid-b64b70253/',
                  ),
                  const SizedBox(height: 20),
                  _buildDeveloperCard(
                    context: context,
                    name: AppLocalizations.of(context)!.yessin,
                    role: AppLocalizations.of(context)!.mobile_dev,
                    imageUrl: 'images/safa.jpg',
                    linkedInUrl: 'https://www.linkedin.com/in/yessin-montassar',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeveloperCard({
    required BuildContext context,
    required String name,
    required String role,
    required String imageUrl,
    required String linkedInUrl,
  }) {
    return GestureDetector(
      onTap: () async {
        if (await canLaunch(linkedInUrl)) {
          await launch(linkedInUrl);
        } else {
          throw 'Could not launch $linkedInUrl';
        }
      },
      child: Card(
        elevation: 5,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: Directionality.of(context) == TextDirection.rtl
                ? MainAxisAlignment.end
                : MainAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(50),
                child: Image.asset(
                  imageUrl,
                  width: 80,
                  height: 80,
                  fit: BoxFit.cover,
                ),
              ),
              const SizedBox(width: 20),
              Expanded( // Utilisation de Expanded pour que la colonne prenne le reste de l'espace
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.visible, // Permet d'afficher tout le texte
                    ),
                    const SizedBox(height: 5),
                    Text(
                      role,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                      overflow: TextOverflow.visible, // Permet d'afficher tout le texte
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }


}
