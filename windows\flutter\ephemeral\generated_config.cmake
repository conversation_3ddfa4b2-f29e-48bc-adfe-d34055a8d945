# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\Desktop\\fluter\\Flutter_SDK\\Flutter_SDK" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Flutter\\projet" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\Desktop\\fluter\\Flutter_SDK\\Flutter_SDK"
  "PROJECT_DIR=C:\\Flutter\\projet"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\Desktop\\fluter\\Flutter_SDK\\Flutter_SDK"
  "FLUTTER_EPHEMERAL_DIR=C:\\Flutter\\projet\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Flutter\\projet"
  "FLUTTER_TARGET=C:\\Flutter\\projet\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Flutter\\projet\\.dart_tool\\package_config.json"
)
